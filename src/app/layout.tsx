import type { Metadata } from 'next';

import Providers from '@/components/providers';

import './globals.css';

export const metadata: Metadata = {
    title: 'Tank admin web app',
    description: 'Generated by create next app',
    icons: 'biohazard.svg',
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en">
            <body>
                <Providers>{children}</Providers>
            </body>
        </html>
    );
}
