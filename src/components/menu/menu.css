.noselect {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.autoWidthMenu {
    width: 58vw;
}

/* 修复菜单项过长问题 */
.ant-menu-item,
.ant-menu-submenu-title {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ant-menu-item-only-child {
    padding-left: 0%;
    padding-right: 0%;
}

span.ant-menu-title-content,
span.ant-menu-title-content > span {
    max-width: 138px;
}

span.ant-menu-title-content > span {
    display: inline-block;
}
